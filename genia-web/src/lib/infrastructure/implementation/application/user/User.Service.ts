import { gql, useQuery } from '@apollo/client';

import { GetTableDataProps } from '#appComponent/table/Table.Type';

const UsersQuery = gql`
  query GetUsers ($limit: Int, $offset: Int, $where: user_bool_exp) {
    user(
      limit: $limit,
      offset: $offset,
      where: $where
    ) {
      id
      email
      name
      lastName
      appPhoneNumber
      disabledAt
      userCompanies {
        company {
          id
          name
        }
        role
      }
    }
    user_aggregate {
      aggregate {
        count
      }
    }
  }
`;

const GetUserQuery = gql`
  query GetUser($id: uuid!) {
    user(where: {id: {_eq: $id}}) {
      id
      email
      name
      lastName
      appPhoneNumber
      disabledAt
      userCompanies {
        company {
          id
          name
        }
        role
      }
    }
  }
`;

const useGetUsers = ({
  limit = 10, offset = 0, searchTerm,
}: GetTableDataProps) => {
  const searchCondition = searchTerm ? { email: { _ilike: `%${searchTerm}%` } } : {};
  const {
    data, loading, error, refetch,
  } = useQuery(UsersQuery, {
    variables: {
      limit,
      offset,
      where: searchCondition,
    },
  });

  return {
    items: data?.user,
    totalNumberOfItems: data?.user_aggregate?.aggregate?.count,
    loading,
    error: error as Error,
    refetch,
  };
};

const useGetUser = (id: string) => {
  const {
    data, loading, error, refetch,
  } = useQuery(GetUserQuery, {
    variables: { id },
    skip: !id, // No ejecutar la query si no hay ID
  });

  // Transformar los datos para que coincidan con la interfaz esperada
  const user = data?.user?.[0];
  const transformedUser = user ? {
    id: user.id,
    email: user.email,
    name: user.name,
    lastName: user.lastName,
    phoneNumber: user.appPhoneNumber,
    disabledAt: user.disabledAt,
    companies: user.userCompanies?.map((uc: any) => uc.company.id) || [],
    role: user.userCompanies?.[0]?.role || 'USER',
  } : null;

  return {
    user: transformedUser,
    loading,
    error: error as Error,
    refetch,
  };
};

const ReadModelUserOrderService = {
  useGetUsers,
  useGetUser,
};

export default ReadModelUserOrderService;
