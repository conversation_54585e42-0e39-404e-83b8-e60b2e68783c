import CreateUserInCompanyCommand from '#application/user/commands/CreateUserInCompany.Command';
import { RetrieveUserCommand } from '#application/user/commands/RetrieveUser.Command';
import UpdateUserCommand from '#application/user/commands/UpdateUser.Command';
import Cause from '#composition/Cause.type';
import Registry from '#composition/ImplementationRegistry';
import Logger from '#composition/Logger';
import CompanyEntity from '#domain/aggregates/company/Company.Entity';
import CompanyOperator from '#domain/aggregates/company/Company.Operator';
import UserEntity, { UserRole } from '#domain/aggregates/user/User.Entity';
import UserOperator from '#domain/aggregates/user/User.Operator';

import RegisterCommand from '../commands/Register.Command';
import ToggleUserStatusCommandHandler, { ToggleUserStatusCommand } from '../commands/ToggleUserStatus.Command';

const logger = Logger.getLogger();

async function register(registerCommand: RegisterCommand): Promise<{ user: UserEntity, company: CompanyEntity }> {
  const {
    user: {
      email, lastName, name, phoneNumber,
    },
    company: {
      name: companyName, description, tributaryId, country,
    },
  } = registerCommand;

  const userByEmail = await Registry.UserRepository.findOneByEmail(email);

  if (userByEmail) {
    throw new Error('Email already exists', { cause: Cause.CONFLICT });
  }

  const userByPhone = await Registry.UserRepository.findOneByPhoneNumber(phoneNumber);

  if (userByPhone) {
    throw new Error('Phone number already exists', { cause: Cause.CONFLICT });
  }

  const existingCompany = await Registry.CompanyRepository.findOneByTributaryIdAndCountry(tributaryId, country);

  if (existingCompany) {
    throw new Error(`Company with ${tributaryId} already exists`, { cause: Cause.CONFLICT });
  }

  return Registry.TransactionService.transactional(async () => {
    const company = await Registry.CompanyRepository.save(CompanyOperator.build({
      id: await Registry.IdentificationService.generateId(),
      name: companyName,
      description,
      tributaryId,
      country,
    }));

    const processingUser = await Registry.UserRepository.createOne(UserOperator.build({
      id: await Registry.IdentificationService.generateId(),
      email,
      companies: [company.id],
      lastName,
      name,
      role: UserRole.ADMIN,
      phoneNumber,
    }));

    return { user: processingUser, company };
  }).catch(() => {
    logger.error('Error registering user', { registerCommand });

    throw new Error('Error registering user');
  });
}

async function createUserInCompany(command: CreateUserInCompanyCommand): Promise<UserEntity> {
  const {
    user: {
      email, lastName, name, phoneNumber, role = UserRole.USER,
    },
    companyId,
  } = command;

  const userByEmail = await Registry.UserRepository.findOneByEmail(email);

  if (userByEmail) {
    throw new Error('Email already exists', { cause: Cause.CONFLICT });
  }

  const userByPhone = await Registry.UserRepository.findOneByPhoneNumber(phoneNumber);

  if (userByPhone) {
    throw new Error('Phone number already exists', { cause: Cause.CONFLICT });
  }

  const company = await Registry.CompanyRepository.findOneById(companyId);

  if (!company) {
    throw new Error(`Company with ID ${companyId} not found`, { cause: Cause.NOT_FOUND });
  }

  const processingUser = await Registry.UserRepository.createOne(UserOperator.build({
    id: await Registry.IdentificationService.generateId(),
    email,
    companies: [companyId],
    lastName,
    name,
    role,
    phoneNumber,
  }));

  return processingUser;
}

async function retrieveUser(command: RetrieveUserCommand): Promise<UserEntity> {
  const { userId } = command;

  const user = await Registry.UserRepository.findOneById(userId);

  if (!user) {
    throw new Error(`User with ID ${userId} not found`, { cause: Cause.NOT_FOUND });
  }

  return user;
}

async function updateUser(command: UpdateUserCommand): Promise<UserEntity> {
  const { userId, user: userUpdates } = command;

  const existingUser = await Registry.UserRepository.findOneById(userId);

  if (!existingUser) {
    throw new Error(`User with ID ${userId} not found`, { cause: Cause.NOT_FOUND });
  }

  // Validar teléfono único si se está actualizando
  if (userUpdates.phoneNumber && userUpdates.phoneNumber !== existingUser.phoneNumber) {
    const userByPhone = await Registry.UserRepository.findOneByPhoneNumber(userUpdates.phoneNumber);
    if (userByPhone) {
      throw new Error('Phone number already exists', { cause: Cause.CONFLICT });
    }
  }

  // Crear usuario actualizado
  const updatedUser: UserEntity = {
    ...existingUser,
    ...userUpdates,
  };

  await Registry.UserRepository.update(updatedUser);

  return updatedUser;
}

async function toggleUserStatus(command: ToggleUserStatusCommand): Promise<UserEntity> {
  const toggleHandler = new ToggleUserStatusCommandHandler(Registry.UserRepository);
  return toggleHandler.execute(command);
}

const UserUseCase = {
  register,
  createUserInCompany,
  retrieveUser,
  updateUser,
  toggleUserStatus,
};

export default UserUseCase;
