import UserRepository from '#application/user/repositories/User.Repository';
import UserEntity from '#domain/aggregates/user/User.Entity';

export interface ToggleUserStatusCommand {
  userId: string;
  disable: boolean;
}

export default class ToggleUserStatusCommandHandler {
  constructor(private userRepository: UserRepository) {}

  async execute(command: ToggleUserStatusCommand): Promise<UserEntity> {
    const { userId, disable } = command;

    const user = await this.userRepository.findOneById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    const updatedUser: UserEntity = {
      ...user,
      disabledAt: disable ? new Date() : null,
    };

    await this.userRepository.update(updatedUser);

    return updatedUser;
  }
}
