import Cause from '#composition/Cause.type';
import UserEntity, { UserRole } from '#domain/aggregates/user/User.Entity';
import { Modify } from '#domain/common/Common.Type';

export type UserOperatorBuildParams = Modify<UserEntity, {
  role?: UserRole;
  disabledAt?: Date | null;
}>;

function build(params: UserOperatorBuildParams) : UserEntity {
  const {
    id, email, companies, lastName, name, phoneNumber, disabledAt,
  } = params;

  if (companies.length === 0) throw new Error('User must have at least one company', { cause: Cause.BAD_REQUEST });

  return {
    id,
    email,
    name,
    lastName,
    companies,
    phoneNumber,
    role: params.role || UserRole.USER,
    disabledAt: disabledAt || null,
  };
}

function addCompany(user: UserEntity, companyId: string): UserEntity {
  if (user.companies.includes(companyId)) throw new Error('User already belongs to this company', { cause: Cause.BAD_REQUEST });

  const modifiedUser = {
    ...user,
    companies: [...user.companies, companyId],
  };

  return modifiedUser;
}

export default {
  build,
  addCompany,
};
