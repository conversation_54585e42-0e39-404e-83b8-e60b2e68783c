import { Router } from 'express';

import ApiKeyMiddleWare from '#infrastructure/ports/http/middlewares/ApiKey.Middleware.Http';
import { uuidRegExp } from '#infrastructure/ports/http/RegExp';

import HandlersHttp from '../../Handlers.Http';

import UserController from './controllers/User.Controller';

function getRoutes(): Router {
  const usersRoutes = Router();

  usersRoutes.post('/register', ApiKeyMiddleWare.validateAccess, HandlersHttp.handleAndCatch(UserController.register));
  usersRoutes.post('/create-in-company', ApiKeyMiddleWare.validateAccess, HandlersHttp.handleAndCatch(UserController.createUserInCompany));
  usersRoutes.put(`/:userId(${uuidRegExp})`, ApiKeyMiddleWare.validateAccess, HandlersHttp.handleAndCatch(UserController.updateUser));
  usersRoutes.patch(`/:userId(${uuidRegExp})/status`, ApiKeyMiddleWare.validateAccess, HandlersHttp.handleAndCatch(UserController.toggleUserStatus));
  usersRoutes.get(`/:userId(${uuidRegExp})/claims`, ApiKeyMiddleWare.validateAccess, HandlersHttp.handleAndCatch(UserController.getClaims));

  return usersRoutes;
}

const UsersRouter = {
  getRoutes,
};

export default UsersRouter;
