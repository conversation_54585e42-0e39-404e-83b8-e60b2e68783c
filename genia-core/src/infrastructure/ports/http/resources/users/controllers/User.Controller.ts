import { Request, Response } from 'express';

import CreateUserInCompanyCommand from '#application/user/commands/CreateUserInCompany.Command';
import RegisterCommand from '#application/user/commands/Register.Command';
import UpdateUserCommand from '#application/user/commands/UpdateUser.Command';
import UserUseCase from '#application/user/useCases/User.UseCase';
import EnvConfiguration from '#infrastructure/configurations/Env.configuration';
import { ClaimsResponseSchemaShape } from '#infrastructure/ports/http/resources/users/schemas/ClaimsResponse.Schema';
import CreateUserInCompanySchema, { createUserInCompanySchema } from '#infrastructure/ports/http/resources/users/schemas/CreateUserInCompany.Schema';
import { createUserInCompanyResponseSchema } from '#infrastructure/ports/http/resources/users/schemas/CreateUserInCompanyResponse.Schema';
import RegisterSchema, { registerSchema } from '#infrastructure/ports/http/resources/users/schemas/Register.Schema';
import { registerResponseSchema } from '#infrastructure/ports/http/resources/users/schemas/RegisterResponse.Schema';
import ToggleUserStatusSchema, { toggleUserStatusSchema } from '#infrastructure/ports/http/resources/users/schemas/ToggleUserStatus.Schema';
import { updateUserSchema } from '#infrastructure/ports/http/resources/users/schemas/UpdateUser.Schema';
import SchemasValidatorUtility from '#infrastructure/utilities/SchemasValidator.Utility';

const { HASURA_DEFAULT_ROLE, CLAIMS_NAMESPACE } = EnvConfiguration.getEnv();

const rfcRegex = /^([A-ZÑ&]{3,4})\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])[A-Z\d]{2}[A\d]$/;
const nitReges = /^\d{5,10}-\d{1}$/;
const cpfOuCnpjRegex = /^(\d{3}\.\d{3}\.\d{3}-\d{2}|\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2})$/;
const cuitCuilRegex = /^\d{2}-\d{8}-\d{1}$/;
const rutRegex = /^(\d{1,2}\.?\d{3}\.?\d{3}-[\dkK])$/;

const countryTID: Record<string, RegExp> = {
  MX: rfcRegex,
  CO: nitReges,
  BR: cpfOuCnpjRegex,
  AR: cuitCuilRegex,
  CL: rutRegex,
};

/**
   * @openapi
   * /users/register:
   *   post:
   *     description: Register a new user, this endpoint is private and only available for admins or to be used by auth third parties
   *     tags:
   *       - Users
   *     summary: Register a new user relating an external id and an email with our domain user
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: "#/components/schemas/RegisterSchema"
   *     responses:
   *       "200":
   *         description: User created
   *         content:
   *           application/json:
   *             schema:
   *               $ref: "#/components/schemas/RegisterResponseSchema"
   *       "400":
   *         description: Invalid input, review the schema.
   *         $ref: "#/components/responses/4XX"
   *       "409":
   *         description: User already exist.
   *         $ref: "#/components/responses/4XX"
   *       "5XX":
   *         $ref: "#/components/responses/5XX"
 */
async function register(req: Request<unknown, unknown, registerSchema>, res: Response<registerResponseSchema>): Promise<Response<registerResponseSchema>> {
  const { body } = req;

  SchemasValidatorUtility.schemasValidation(RegisterSchema, body);

  const {
    user: {
      email, name, lastName, phoneNumber,
    },
    company: {
      name: companyName, description, tributaryId, country,
    },
  } = body;

  if (!countryTID[country]) throw new Error('Invalid country', { cause: 'BAD_REQUEST' });

  if (!countryTID[country].test(tributaryId)) {
    throw new Error(`Invalid tributary ID for country ${country}`, { cause: 'BAD_REQUEST' });
  }

  const command: RegisterCommand = {
    user: {
      email,
      name,
      lastName,
      phoneNumber,
    },
    company: {
      name: companyName,
      description,
      tributaryId,
      country,
    },
  };

  const { company, user } = await UserUseCase.register(command);

  return res.send({
    user,
    company,
  });
}

/**
   * @openapi
   * /users/claims:
   *   post:
   *     description: Get claims for a user, this endpoint returns the metadata claims required for authentication
   *     tags:
   *       - Users
   *     summary: Get authentication claims for a registered user
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               userId:
   *                 type: string
   *               companyId:
   *                 type: string
   *             required:
   *               - userId
   *               - companyId
   *     responses:
   *       "200":
   *         description: Claims retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               $ref: "#/components/schemas/ClaimsResponseSchema"
   *       "400":
   *         description: Invalid input.
   *         $ref: "#/components/responses/4XX"
   *       "404":
   *         description: User not found.
   *         $ref: "#/components/responses/4XX"
   *       "5XX":
   *         $ref: "#/components/responses/5XX"
 */
async function createUserInCompany(
  req: Request<unknown, unknown, createUserInCompanySchema>,
  res: Response<createUserInCompanyResponseSchema>,
): Promise<Response<createUserInCompanyResponseSchema>> {
  const { body } = req;

  SchemasValidatorUtility.schemasValidation(CreateUserInCompanySchema, body);

  const {
    user: {
      email, name, lastName, phoneNumber, role,
    },
    companyId,
  } = body;

  const command: CreateUserInCompanyCommand = {
    user: {
      email,
      name,
      lastName,
      phoneNumber,
      role,
    },
    companyId,
  };

  const user = await UserUseCase.createUserInCompany(command);

  return res.send(user);
}

async function updateUser(
  req: Request<{userId: string}, unknown, updateUserSchema>,
  res: Response<any>,
): Promise<Response<any>> {
  const { params: { userId }, body } = req;

  // SchemasValidatorUtility.schemasValidation(UpdateUserSchema, body);

  const command: UpdateUserCommand = {
    userId,
    user: body,
  };

  const updatedUser = await UserUseCase.updateUser(command);

  return res.send(updatedUser);
}

async function toggleUserStatus(
  req: Request<{userId: string}, unknown, toggleUserStatusSchema>,
  res: Response<any>,
): Promise<Response<any>> {
  const { params: { userId }, body } = req;

  SchemasValidatorUtility.schemasValidation(ToggleUserStatusSchema, body);

  const { disable } = body;

  const updatedUser = await UserUseCase.toggleUserStatus({ userId, disable });

  return res.send(updatedUser);
}

async function getClaims(
  req: Request<{userId: string}, unknown, unknown>,
  res: Response<ClaimsResponseSchemaShape>,
): Promise<Response<ClaimsResponseSchemaShape>> {
  const { params: { userId } } = req;

  const { id, companies: [companyId] } = await UserUseCase.retrieveUser({ userId });

  return res.send({
    metadata: {
      claims: {
        [CLAIMS_NAMESPACE]: {
          'user-id': id,
          'company-id': companyId,
        },
        'https://hasura.io/jwt/claims': {
          'x-hasura-allowed-roles': [HASURA_DEFAULT_ROLE],
          'x-hasura-default-role': HASURA_DEFAULT_ROLE,
          'x-hasura-user-id': id,
          'x-hasura-company-id': companyId,
        },
      },
    },
  });
}

const UserController = {
  register,
  createUserInCompany,
  updateUser,
  toggleUserStatus,
  getClaims,
};

export default UserController;
