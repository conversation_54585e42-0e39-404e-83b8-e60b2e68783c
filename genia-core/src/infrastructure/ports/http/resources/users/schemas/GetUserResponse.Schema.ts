import { JSONSchemaType } from 'ajv';

import { UserRole } from '#domain/aggregates/user/User.Entity';

export interface getUserResponseSchema {
  id: string;
  email: string;
  name?: string;
  lastName?: string;
  phoneNumber: string;
  companies: string[];
  role: UserRole;
  disabledAt?: Date | null;
}

const GetUserResponseSchema: JSONSchemaType<getUserResponseSchema> = {
  type: 'object',
  properties: {
    id: { type: 'string' },
    email: { type: 'string' },
    name: { type: 'string', nullable: true },
    lastName: { type: 'string', nullable: true },
    phoneNumber: { type: 'string' },
    companies: { type: 'array', items: { type: 'string' } },
    role: { type: 'string', enum: Object.values(UserRole) },
    disabledAt: { type: 'string', format: 'date-time', nullable: true },
  },
  required: ['id', 'email', 'phoneNumber', 'companies', 'role'],
  additionalProperties: false,
};

export default GetUserResponseSchema;
