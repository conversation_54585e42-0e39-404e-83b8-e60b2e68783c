import { JSONSchemaType } from 'ajv';

import { UserRole } from '#domain/aggregates/user/User.Entity';

export interface updateUserSchema {
  name?: string;
  lastName?: string;
  phoneNumber?: string;
  role?: UserRole;
  disabledAt?: Date | null;
}

const UpdateUserSchema: JSONSchemaType<updateUserSchema> = {
  type: 'object',
  properties: {
    name: { type: 'string', nullable: true },
    lastName: { type: 'string', nullable: true },
    phoneNumber: { type: 'string', nullable: true },
    role: { type: 'string', enum: Object.values(UserRole), nullable: true },
    disabledAt: { type: 'string', format: 'date-time', nullable: true },
  },
  additionalProperties: false,
};

export default UpdateUserSchema;
