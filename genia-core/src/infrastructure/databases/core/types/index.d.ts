import { Knex } from 'knex';

import { CatalogDiscountType } from '#domain/aggregates/catalogDiscount/CatalogDiscount.Entity';
import { InventoryMediaSize } from '#domain/aggregates/inventoryMedia/InventoryMedia.Entity';
import { PurchaseOrderStatus } from '#domain/aggregates/purchaseOrder/PurchaseOrder.Entity';
import { SaleOrderStatus } from '#domain/aggregates/saleOrder/SaleOrder.Entity';
import { StoreDiscountType } from '#domain/aggregates/storeDiscount/StoreDiscount.Entity';
import { TaxType } from '#domain/aggregates/tax/Tax.Entity';
import { UserRole } from '#domain/aggregates/user/User.Entity';
import { MediaType } from '#domain/common/aggregates/media/MediaEntity';
import { AttributeType } from '#domain/common/Common.Type';
import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

interface commonColumns {
  createdAt: Date;
  updatedAt: Date;
}

enum InventoryType {
  COMMODITY = 'commodity',
  PRODUCT_INPUT = 'product_input',
}

type createdAt = Omit<commonColumns, 'updatedAt'>;

type dbUser = { id: string, email: string, name?: string, lastName?: string, appPhoneNumber: string, disabledAt: Date | null } & commonColumns;
type dbUserCompany = {
  userId: string;
  companyId: string,
  role: UserRole,
  verifiedAt?: Date | null,
} & commonColumns;
type dbCompany = { id: string; name: string, tributaryId: string, country: string, description?: string} & commonColumns;
type dbInventory = {
  id: string;
  sku: string;
  name: string;
  description: string | null;
  stock: number;
  attributes: AttributeType[] | null;
  companyId: string;
  standardIdentifier: string | null;
  type: InventoryType;
  measurementUnit: string;
  hasStockValidation: boolean;
  disabledAt: Date | null;
  restrictedStock: number;
} & commonColumns;
type dbCatalogNoTimestamps = {
  id: string;
  readId: string;
  name: string;
  description: string;
  attributes: AttributeType[] | null
  price: number;
  requiresStock: boolean;
  type: CatalogType;
  companyId: string;
  disabledAt: Date | null;
};
type dbCatalog = dbCatalogNoTimestamps & commonColumns;
type dbInventoryCatalog = {
  catalogId: string;
  inventoryId: string;
  quantity: number;
};
type dbCatalogDiscount = {
  id: string;
  discountValue: number;
  discountType: CatalogDiscountType;
  name: string;
  requiredQuantity: number;
  companyId: string;
  startDate: Date | null;
  endDate: Date | null;
  disabledAt: Date | null;
};
type dbStoreDiscount = {
  id: string;
  discountValue: number;
  discountType: StoreDiscountType;
  name: string;
  requiredAmount: number;
  companyId: string;
  startDate: Date | null;
  endDate: Date | null;
  disabledAt: Date | null;
} & commonColumns;

type dbTax = {
  id: string;
  name: string;
  value: number;
  type: TaxType;
  countryCode: string;
  disabledAt: Date | null;
} & commonColumns;
type dbCatalogDiscountCatalog = {
  catalogId: string;
  catalogDiscountId: string;
};
type dbCatalogDiscountClient = {
  clientId: string;
  catalogDiscountId: string;
};
type dbCatalogTax = {
  catalogId: string;
  taxId: string;
};
type dbProvider = {
  id: string;
  name: string;
  tributaryId: string | null;
  providerCompanyId: string | null;
  companyId: string;
} & commonColumns;

type dbClient = {
  id: string;
  name: string;
  tributaryId: string | null;
  clientCompanyId: string | null;
  companyId: string;
} & commonColumns;
type dbProviderInventory = {
  inventoryId: string;
  providerId: string;
  currentPurchasePrice: number;
  currentDiscount: number | null;
  providerProductSku: string | null;
};
type dbStoreDiscountClient = {
  storeDiscountId: string;
  clientId: string;
};
type dbInventoryIndex = {
  inventoryId: string;
  companyId: string;
  indexedText: string;
};
type dbCatalogIndex = {
  catalogId: string;
  companyId: string;
  indexedText: string;
};
type dbCompanySequence = {
  companyId: string;
  entity: string;
  prefix: string;
  nextValue: number;
  disabledAt: Date | null;
};

type dbSaleOrder = {
  id: string;
  clientId: string;
  companyId: string;
  assignedUserId: string | null;
  readId: string;
  status: SaleOrderStatus;
  deliveryDate: Date | null;
  shippedAt: Date | null;
  reviewStartedAt: Date | null;
  shippingAddress: string | null;
  subtotalBeforeDiscount: number;
  subtotal: number;
  totalDiscount: number;
  totalTaxes: number;
  shippingPrice: number;
  total: number;
  notes: string | null;
  taxes: {
    name: string;
    value: number;
    type: TaxType;
    amount: number;
  }[];
  relatedPurchaseOrderId: string | null;
} & commonColumns;

type dbSaleOrderItem = {
  saleOrderId: string;
  catalogId: string;
  productId: string;
  name: string;
  quantity: number;
  unitPrice: number;
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  subtotal: number;
  total: number;
  discountType: 'percentage' | 'amount' | null;
  discountValue: number | null;
  taxes: {
    name: string;
    value: number;
    type: TaxType;
    amount: number;
  }[];
} & commonColumns;

type dbCatalogMedia = {
  id: string;
  catalogId: string;
  url: string;
  mediaType: MediaType;
  processing: boolean;
} & commonColumns;

type dbInventoryMedia = {
  id: string;
  inventoryId: string;
  url: string;
  mediaType: MediaType;
  size: InventoryMediaSize;
  processing: boolean;
} & commonColumns;

type dbPurchaseOrder = {
  id: string;
  readId: string;
  status: PurchaseOrderStatus;
  shippingAddress?: string | null;
  shippedAt?: Date | null;
  reviewStartedAt?: Date | null;
  shippingAddress?: string | null;
  assignedUserId: string | null;
  providerId: string;
  companyId: string;
  deliveryDate: Date | null;
  subtotalBeforeDiscount: number;
  subtotal: number;
  totalDiscount: number;
  totalTaxes: number;
  shippingPrice?: number;
  total: number;
  notes: string | null;
  taxes: {
    name: string;
    value: number;
    type: TaxType;
    amount: number;
  }[];
  relatedSaleOrderId: string | null;
} & commonColumns;

type dbPurchaseOrderItem = {
  purchaseOrderId: string;
  referenceId: string;
  name: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  subtotal: number;
  total: number;
  discountType: 'percentage' | 'amount' | null;
  discountValue: number | null;
  inventoryId: string | null;
  taxes: {
    name: string;
    value: number;
    type: TaxType;
    amount: number;
  }[];
} & commonColumns;

type dbQuote = {
  id: string;
  clientId: string;
  companyId: string;
  assignedUserId: string | null;
  readId: string;
  status: QuoteStatus;
  promisedDeliveryDate: Date | null;
  shippingAddress: string | null;
  subtotalBeforeDiscount: number;
  subtotal: number;
  totalDiscount: number;
  totalTaxes: number;
  shippingPrice: number;
  total: number;
  taxes: {
    name: string;
    value: number;
    type: TaxType;
    amount: number;
  }[];
} & commonColumns;

type dbQuoteItem = {
  catalogId: string;
  productId: string;
  name: string;
  quantity: number;
  unitPrice: number;
  unitPriceAfterDiscount: number;
  unitPriceAfterDiscountAndTaxes: number;
  subtotal: number;
  total: number;
  discountType: 'percentage' | 'amount' | null;
  discountValue: number | null;
  taxes: {
    name: string;
    value: number;
    type: TaxType;
    amount: number;
  }[];
} & commonColumns;

type dbQuoteClientFields = {
  id: string;
  quoteId: string;
  relatedPurchaseOrderId: string | null;
  notes: string | null;
} & commonColumns;

type dbQuoteProviderFields = {
  id: string
  quoteId: string;
  relatedSaleOrderId: string | null;
  notes: string | null;
} & commonColumns;

type dbInventoryHistory = {
  id: string;
  inventoryId: string;
  quantity: number;
  measurementUnit: string;
  movementType: InventoryMovementType;
  userId: string;
  reason: string;
  createdAt: Date;
};

type dbCatalogMedia = {
  id: string;
  catalogId: string;
  url: string;
  mediaType: MediaType;
  processing: boolean;
  createdAt: Date;
  updatedAt: Date;
};

declare module 'knex/types/tables' {

  interface Tables {
    [TableNamesConfiguration.USER]: Knex.CompositeTableType<
    dbUser,
    Omit<dbUser, 'id' | 'createdAt' | 'updatedAt'>,
    Partial<dbUser>,
    >;
    [TableNamesConfiguration.USER_COMPANY]: Knex.CompositeTableType<
    dbUserCompany,
    Omit<dbUserCompany, 'id' | 'createdAt' | 'updatedAt'>,
    never,
    >;
    [TableNamesConfiguration.USER_REFERENCE]: Knex.CompositeTableType<
    dbUserReference,
    Omit<dbUserReference, 'id' | 'createdAt'>,
    never,
    >;
    [TableNamesConfiguration.COMPANY]: Knex.CompositeTableType<
    dbCompany,
    Omit<dbCompany, 'id' | 'createdAt' | 'updatedAt'>,
    Partial<dbCompany> & { id: string },
    >;
    [TableNamesConfiguration.INVENTORY]: Knex.CompositeTableType<
    dbInventory,
    Omit<dbInventory, 'createdAt' | 'updatedAt'> & Partial<commonColumns>,
    >;
    [TableNamesConfiguration.CATALOG]: Knex.CompositeTableType<
    dbCatalog,
    dbCatalogNoTimestamps,
    Partial<dbCatalog> & { id: string },
    >;
    [TableNamesConfiguration.INVENTORY_CATALOG]: Knex.CompositeTableType<
    dbInventoryCatalog,
    dbInventoryCatalog,
    never,
    >;
    [TableNamesConfiguration.CATALOG_DISCOUNT]: Knex.CompositeTableType<
    dbCatalogDiscount,
    dbCatalogDiscount,
    Partial<dbCatalogDiscount> & { id: string },
    >;
    [TableNamesConfiguration.TAX]: Knex.CompositeTableType<
    dbTax,
    Omit<dbTax, 'id' | 'createdAt' | 'updatedAt'>,
    Partial<dbTax> & { id: string },
    >;
    [TableNamesConfiguration.CATALOG_DISCOUNT_CATALOG]: Knex.CompositeTableType<
    dbCatalogDiscountCatalog,
    dbCatalogDiscountCatalog,
    never,
    >;
    [TableNamesConfiguration.CATALOG_DISCOUNT_CLIENT]: Knex.CompositeTableType<
    dbCatalogDiscountClient,
    dbCatalogDiscountClient,
    never,
    >;
    [TableNamesConfiguration.CATALOG_TAX]: Knex.CompositeTableType<
    dbCatalogTax,
    dbCatalogTax,
    never,
    >;
    [TableNamesConfiguration.PROVIDER]: Knex.CompositeTableType<
    dbProvider,
    Omit<dbProvider, 'createdAt' | 'updatedAt'> & Partial<commonColumns>,
    Partial<dbProvider> & { id: string },
    >
    [TableNamesConfiguration.CLIENT]: Knex.CompositeTableType<
    dbClient,
    Omit<dbClient, 'createdAt' | 'updatedAt'>,
    Partial<dbClient> & { id: string },
    >;
    [TableNamesConfiguration.PROVIDER_INVENTORY]: Knex.CompositeTableType<
    dbProviderInventory,
    dbProviderInventory,
    >;
    [TableNamesConfiguration.STORE_DISCOUNT]: Knex.CompositeTableType<
    dbStoreDiscount,
    dbStoreDiscount,
    Partial<dbStoreDiscount> & { id: string },
    >;
    [TableNamesConfiguration.STORE_DISCOUNT_CLIENT]: Knex.CompositeTableType<
    dbStoreDiscountClient,
    dbStoreDiscountClient,
    never,
    >;
    [TableNamesConfiguration.INVENTORY_INDEX]: Knex.CompositeTableType<
    dbInventoryIndex,
    dbInventoryIndex,
    never,
    >;
    [TableNamesConfiguration.CATALOG_INDEX]: Knex.CompositeTableType<
    dbCatalogIndex,
    dbCatalogIndex,
    never,
    >;
    [TableNamesConfiguration.COMPANY_SEQUENCE]: Knex.CompositeTableType<
    dbCompanySequence,
    dbCompanySequence,
    never,
    >;
    [TableNamesConfiguration.CATALOG_MEDIA]: Knex.CompositeTableType<
    dbCatalogMedia,
    dbCatalogMedia,
    never,
    >;
    [TableNamesConfiguration.PURCHASE_ORDER]: Knex.CompositeTableType<
    dbPurchaseOrder,
    dbPurchaseOrder,
    Partial<dbPurchaseOrder> & { id: string },
    >;
    [TableNamesConfiguration.PURCHASE_ORDER_ITEM]: Knex.CompositeTableType<
    dbPurchaseOrderItem,
    dbPurchaseOrderItem,
    Partial<dbPurchaseOrderItem> & { purchaseOrderId: string, referenceId: string }
    >;
    [TableNamesConfiguration.INVENTORY_HISTORY]: Knex.CompositeTableType<
    dbInventoryHistory,
    dbInventoryHistory,
    never,
    >
    [TableNamesConfiguration.CATALOG_MEDIA]: Knex.CompositeTableType<
    dbCatalogMedia,
    dbCatalogMedia,
    Partial<dbCatalogMedia> & { id: string },
    >
    [TableNamesConfiguration.INVENTORY_MEDIA]: Knex.CompositeTableType<
    dbInventoryMedia,
    dbInventoryMedia,
    Partial<dbInventoryMedia> & { id: string },
    >
  }
}
