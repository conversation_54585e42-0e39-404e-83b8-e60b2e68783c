import type { Knex } from 'knex';

import TableNamesConfiguration from '#infrastructure/configurations/TableNames.configuration';

export async function up(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.USER, (table: Knex.AlterTableBuilder) => {
    table.timestamp('disabled_at').nullable();
  });
}

export async function down(knex: Knex): Promise<void> {
  await knex.schema.alterTable(TableNamesConfiguration.USER, (table: Knex.AlterTableBuilder) => {
    table.dropColumn('disabled_at');
  });
}
